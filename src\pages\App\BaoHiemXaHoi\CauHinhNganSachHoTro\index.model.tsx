/**
 * MODEL QUẢN LÝ TỈNH THÀNH
 * - <PERSON><PERSON><PERSON> nghĩa TypeScript interfaces cho module quản lý tỉnh thành
 * - <PERSON><PERSON> tả cấu trúc dữ liệu cho Context props
 * - <PERSON><PERSON>m bảo type safety cho toàn bộ module
 */
import {ReactQuery} from "@src/@types";

export interface ICauHinhNganSachHoTroContextProps {
  listTinhThanh: Array<CommonExecute.Execute.IDanhMucTinhThanh>; // Danh sách tỉnh thành hiển thị trong bảng
  tongSoDong: number; // Tổng số bản ghi để hiển thị pagination
  loading: boolean; // Trạng thái loading cho UI
  danhSachNganSachHoTroNgayApDung: Array<CommonExecute.Execute.INganSachHoTroNgayApDung>; // Danh sách ngân sách hỗ trợ theo ngày áp dụng
  filterParams: ReactQuery.ITimKiemPhanTrangDanhMucTinhThanhParams & ReactQuery.IPhanTrang; // Tham số filter và phân trang
  chiTietTinhThanh: CommonExecute.Execute.IDanhMucTinhThanh; // Chi tiết tỉnh thành
  chiTietNganSachHoTro: CommonExecute.Execute.INganSachHoTro; // Chi tiết ngân sách hỗ trợ
  listDonViThuHo: Array<CommonExecute.Execute.IDonViThuHo>; // Danh sách đơn vị thu hộ
  CapNhatNganSachHoTro: (params: ReactQuery.ICapNhatNganSachHoTroParams) => Promise<boolean>; // Cập nhật ngân sách hỗ trợ
  layChiTietNganSachHoTro: (params: ReactQuery.IChiTietNganSachHoTroParams) => Promise<CommonExecute.Execute.INganSachHoTro>; // Lấy chi tiết ngân sách hỗ trợ
  getListTinhThanh: (params?: ReactQuery.ITimKiemPhanTrangDanhMucTinhThanhParams & ReactQuery.IPhanTrang) => Promise<void>; // Tìm kiếm danh sách tỉnh thành
  layDanhSachNganSachHoTroNgayApDung: (params: ReactQuery.ILietKeNganSachHoTroNgayApDungParams) => Promise<CommonExecute.Execute.INganSachHoTroNgayApDung>; // Lấy danh sách ngân sách hỗ trợ theo ngày áp dụng
  layChiTietDanhMucTinhThanh: (params: ReactQuery.IChiTietDanhMucTinhThanhParams) => Promise<CommonExecute.Execute.IDanhMucTinhThanh>; // Lấy chi tiết danh mục tỉnh thành
  setFilterParams: React.Dispatch<React.SetStateAction<ReactQuery.ITimKiemPhanTrangDanhMucTinhThanhParams & ReactQuery.IPhanTrang>>; // Cập nhật tham số filter
  updateNganSachHoTroNgayApDung: (params: ReactQuery.IUpdateNganSachHoTroNgayApDungParams) => Promise<boolean>; // Cập nhật ngân sách hỗ trợ ngày áp dụng
  xoaNgayApDungNganSachHoTro: (params: ReactQuery.IUpdateNganSachHoTroNgayApDungParams) => Promise<boolean>; // Xóa ngày áp dụng ngân sách hỗ trợ
}
