import {CheckOutlined} from "@ant-design/icons";
import {IFormInput, ReactQuery} from "@src/@types";
import {Button, FormInput, HeaderModal} from "@src/components";
import {Col, Flex, Form, Modal, Row} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useState} from "react";
import {FormThemNgayApDung, IModalThemNganSachHoTroNgayADRef, PropThems} from "./index.configs";
import {useCauHinhNganSachHoTroContext} from "../index.context";
import dayjs from "dayjs";
const {ngay_ad, ma_dvi} = FormThemNgayApDung;

const ModalThemNganSachHoTroNgayADComponent = forwardRef<IModalThemNganSachHoTroNgayADRef, PropThems>(({}: PropThems, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataNgayAD?: CommonExecute.Execute.IDonViThuHo) => {
      setIsOpen(true);
      if (dataNgayAD) setChiTietNganSachHoTroNgayAD(dataNgayAD); // nếu có dữ liệu -> set chi tiết DonViThuHo -> là sửa
    },
    close: () => setIsOpen(false),
  }));
  const [chiTietNganSachHoTroNgayAD, setChiTietNganSachHoTroNgayAD] = useState<CommonExecute.Execute.INganSachHoTroNgayApDung | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  const {updateNganSachHoTroNgayApDung, listDonViThuHo, loading, chiTietTinhThanh, layDanhSachNganSachHoTroNgayApDung} = useCauHinhNganSachHoTroContext();
  const [form] = Form.useForm();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const formValues = Form.useWatch([], form);
  // init form data - load dữ liệu vào form khi sửa
  useEffect(() => {
    if (chiTietNganSachHoTroNgayAD) {
      const arrFormData = [];
      for (const key in chiTietNganSachHoTroNgayAD) {
        arrFormData.push({
          name: key,
          value: chiTietNganSachHoTroNgayAD[key as keyof CommonExecute.Execute.INganSachHoTroNgayApDung],
        });
      }
      form.setFields(arrFormData);
    }
  }, [chiTietNganSachHoTroNgayAD]);

  //xử lý validate form
  useEffect(() => {
    form
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [form, formValues]);

  const closeModal = useCallback(() => {
    setIsOpen(false);
    setChiTietNganSachHoTroNgayAD(null);
    form.resetFields();
    layDanhSachNganSachHoTroNgayApDung({ma_tinh: chiTietTinhThanh?.ma, ngay_ad: chiTietTinhThanh?.ngay_ad});
  }, []);

  //Bấm Update
  const onConfirm = async () => {
    try {
      const values: ReactQuery.IUpdateNganSachHoTroNgayApDungParams = form.getFieldsValue(); //lấy ra values của form
      const params = {
        ...values,
        ma_tinh: chiTietTinhThanh?.ma,
        ngay_ad: Number(dayjs(values.ngay_ad, "DD/MM/YYYY").format("YYYYMMDD")),
      };
      await updateNganSachHoTroNgayApDung(params); //cập nhật lại DonViThuHo
      closeModal();
    } catch (error) {
      console.log("onConfirm", error);
    }
  };

  // RENDER
  //FOOTER
  const renderFooter = () => {
    return (
      <Button type="primary" onClick={onConfirm} icon={<CheckOutlined />}>
        Lưu
      </Button>
    );
  };
  const renderFormColum = (props: IFormInput, span = 12) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  const renderForm = () => (
    <Form form={form} layout="vertical">
      <Row gutter={16}>
        {renderFormColum({...ngay_ad}, 8)}
        {renderFormColum({...ma_dvi, options: listDonViThuHo}, 16)}
      </Row>
    </Form>
  );
  //Render
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        title={<HeaderModal title={"Tạo mới ngày áp dụng"} />}
        // centered
        maskClosable={false}
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width="30%"
        style={{top: 200}}
        styles={
          {
            //   body: {
            //     paddingTop: "8px",
            //     paddingBottom: "16px",
            //   },
          }
        }
        footer={renderFooter}>
        {renderForm()}
      </Modal>
    </Flex>
  );
});

ModalThemNganSachHoTroNgayADComponent.displayName = "ModalThemNganSachHoTroNgayADComponent";
export const ModalThemNganSachHoTroNgayAD = memo(ModalThemNganSachHoTroNgayADComponent, isEqual);
