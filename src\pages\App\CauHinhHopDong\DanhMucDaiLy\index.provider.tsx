import {ReactQuery} from "@src/@types";
import {ACTION_CODE} from "@src/constants";
import {CommonExecuteResponse, useCommonExecute} from "@src/services/react-queries";
import {message} from "antd";
import React, {PropsWithChildren, useCallback, useEffect, useMemo, useState} from "react";
import {DanhMucDaiLyContext} from "./index.context";
import {DanhMucDaiLyContextProps} from "./index.model";

/* 
  file Provider : Chứa logic quản lý state, gọi API, wrap context
*/

const DanhMucDaiLyProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;
  const mutateUseCommonExecute = useCommonExecute();
  const [danhSachDaiLy, setDanhSachDaiLy] = useState<Array<CommonExecute.Execute.IDanhMucDaiLy>>([]);
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  const [listDoiTac, setListDoiTac] = useState<Array<CommonExecute.Execute.IDoiTac>>([]);
  const [daiLyQuanLy, setDaiLyQuanLy] = useState<Array<NonNullable<CommonExecute.Execute.IChiTietDanhMucDaiLy["dl_qly"]>[0]>>([]);
  const [daiLyQuanLySelected, setDaiLyQuanLySelected] = useState<Array<NonNullable<CommonExecute.Execute.IChiTietDanhMucDaiLy["dl_qly"]>[0]>>([]);
  const [filterParams, setFilterParams] = useState<ReactQuery.ITimKiemPhanTrangDanhSachDaiLyParams & ReactQuery.IPhanTrang>({
    ma_doi_tac_ql: "",
    ma: "",
    ten: "",
    loai: "",
    trang_thai: "",
    trang: 1,
    // so_dong: ,
  });
  useEffect(() => {
    initData();
  }, []);

  const initData = () => {
    layDanhSachDaiLy(filterParams);
    getListDoiTac();
  };
  useEffect(() => {
    layDanhSachDaiLy(filterParams);
  }, [filterParams]);
  /* ĐỐI TÁC */
  const getListDoiTac = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_DANH_SACH_DOI_TAC,
      });
      setListDoiTac(response?.data?.map(item => ({...item, ten: item.ma + " - " + item.ten})));
    } catch (error) {
      console.log("getListDoiTac error ", error);
    }
  }, [mutateUseCommonExecute]);

  //DS đại lý phân trang
  const layDanhSachDaiLy = useCallback(
    async (body: ReactQuery.ITimKiemPhanTrangDanhSachDaiLyParams & ReactQuery.IPhanTrang) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DS_DAI_LY,
        };

        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        const data = response.data.data || [];
        const tongSoDong = response.data.tong_so_dong || 0;

        // setDanhSachDaiLy(data);
        // setTongSoDong(response.data.tong_so_dong);
        return {
          data,
          tong_so_dong: tongSoDong,
        };
      } catch (error: any) {
        console.log("Lấy danh sách đại lý error:", error.message || error);
        return {
          data: [],
          tong_so_dong: 0,
        };
      }
    },
    [mutateUseCommonExecute],
  );
  //Lấy chi tiết 1 đại lý
  const layChiTietDaiLy = useCallback(
    async (item: ReactQuery.IChiTietDanhMucDaiLyParams): Promise<CommonExecute.Execute.IChiTietDanhMucDaiLy | null> => {
      try {
        const params = {
          ma: item.ma,
          actionCode: ACTION_CODE.GET_CHI_TIET_DAI_LY,
        };
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        const data = responseData.data as CommonExecute.Execute.IChiTietDanhMucDaiLy;
        setDaiLyQuanLy((data.dl_qly ?? []) as Array<NonNullable<CommonExecute.Execute.IChiTietDanhMucDaiLy["dl_qly"]>[0]>);
        // const result = data.dl[0] as CommonExecute.Execute.IChiTietDanhMucDaiLy;
        return data.dl[0] as CommonExecute.Execute.IChiTietDanhMucDaiLy;
      } catch (error: any) {
        console.log("layChiTietdaily error ", error.message || error);
        return null;
      }
    },
    [mutateUseCommonExecute],
  );

  //Cập nhật hoặc tạo mới 1 đại lý
  const onUpdateDanhMucDaiLy = useCallback(
    async (body: ReactQuery.IUpdateDaiLyParams): Promise<number | null | undefined> => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.LUU_CAP_NHAT_DAI_LY,
        };
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if (responseData && (responseData.data as unknown as number) === -1) {
          message.success("Cập nhật thông tin thành công!");
          initData();
          // Chuyển đổi responseData.data thành number
          return responseData.data as unknown as number; // Chuyển đổi responseData.data thành number
        }
      } catch (error: any) {
        console.log("onUpdateDaiLy error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );

  //khai báo giá trị của context để truyền vào Component
  const value = useMemo<DanhMucDaiLyContextProps>(
    () => ({
      listDoiTac,
      tongSoDong,
      danhSachDaiLy,
      loading: mutateUseCommonExecute.isLoading,
      filterParams,
      daiLyQuanLy,
      daiLyQuanLySelected,
      setDaiLyQuanLySelected,
      setFilterParams,
      getListDoiTac,
      onUpdateDanhMucDaiLy,
      layDanhSachDaiLyPhanTrang: layDanhSachDaiLy,
      layChiTietDaiLy,
    }),
    [
      danhSachDaiLy,
      mutateUseCommonExecute,
      filterParams,
      daiLyQuanLy,
      daiLyQuanLySelected,
      tongSoDong,
      listDoiTac,
      setDaiLyQuanLySelected,
      setFilterParams,
      onUpdateDanhMucDaiLy,
      layDanhSachDaiLy,
      layChiTietDaiLy,
      getListDoiTac,
    ],
  );

  return <DanhMucDaiLyContext.Provider value={value}>{children}</DanhMucDaiLyContext.Provider>;
};

export default DanhMucDaiLyProvider;
